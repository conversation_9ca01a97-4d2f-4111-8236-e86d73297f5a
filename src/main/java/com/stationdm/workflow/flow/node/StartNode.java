
package com.stationdm.workflow.flow.node;

import com.fasterxml.jackson.databind.JsonNode;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.flow.base.WorkflowNode;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class StartNode extends WorkflowNode {

    private JsonNode input;

    @Override
    public Mono<Void> execute(WorkflowContext context) {
        return Mono.empty();
    }
}