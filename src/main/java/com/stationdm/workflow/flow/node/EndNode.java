
package com.stationdm.workflow.flow.node;

import com.fasterxml.jackson.databind.JsonNode;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.engine.annotation.Evaluate;
import com.stationdm.workflow.flow.base.WorkflowNode;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class EndNode extends WorkflowNode {

    @Evaluate
    private JsonNode output;

    @Override
    public Mono<Void> execute(WorkflowContext context) {
        return Mono.fromRunnable(() -> {
            // 告知前端“整个工作流已完成”
            context.emitStreamingEvent("WORKFLOW_COMPLETE", this.getId(), "ok");
            // 关闭事件总线（SSE 订阅会 onComplete）
            context.completeEventStream();
        });
    }


}