package com.stationdm.workflow.flow.container;

import com.stationdm.workflow.engine.NodeInputProcessor;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.flow.base.WorkflowNode;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class SequenceContainer extends WorkflowNode {
    private List<WorkflowNode> children;

    @Override
    public Mono<Void> execute(WorkflowContext context) {
        if (children == null || children.isEmpty()) {
            return Mono.empty();
        }

        // 从上下文中获取处理器
        var processor = context.getRequiredBean(NodeInputProcessor.class);

        Mono<Void> chain = Mono.empty();
        for (WorkflowNode node : children) {
            chain = chain.then(Mono.defer(() -> {
                // 在执行节点前，先处理输入
                processor.process(node, context);
                // 然后正常执行节点
                return node.execute(context);
            }));
        }
        return chain;
    }
}