package com.stationdm.workflow.flow.base;

import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.handler.ChatHandler;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public abstract class ChatNode extends WorkflowNode  {

    private final Map<String, List<String>> chatHistory = new HashMap<>();

    protected Mono<String> requestUserInput(String prompt, WorkflowContext context) {
        String interactionId = context.generateInteractionId(getId());
        context.emitStreamingEvent("REQUEST_USER_INPUT", interactionId, prompt);

        ChatHandler chatHandler = context.getRequiredBean(ChatHandler.class);
        return chatHandler.waitForUserInput(interactionId, prompt, 5);
    }

    protected void sendStreamingMessage(String message, WorkflowContext context) {
        context.emitStreamingEvent("NODE_MESSAGE", getId(), message);
    }

    protected Mono<Void> sendStreamingMessage(Flux<String> messageStream, WorkflowContext context) {
        return messageStream
                .doOnNext(chunk -> context.emitStreamingEvent("NODE_MESSAGE_CHUNK", getId(), chunk))
                .doOnComplete(() -> context.emitStreamingEvent("NODE_MESSAGE_END", getId(), "Stream completed"))
                .doOnError(error -> context.emitStreamingEvent("NODE_MESSAGE_ERROR", getId(), "Error in stream: " + error.getMessage()))
                .then();
    }
    
    /**
     * 保存聊天历史
     * @param key 聊天历史的键
     * @param value 要保存的值
     */
    protected void saveChatHistory(String key, String value) {
        chatHistory.computeIfAbsent(key, k -> new ArrayList<>()).add(value);
    }
    
    /**
     * 获取聊天历史
     * @param key 聊天历史的键
     * @param historyWindowSize 历史窗口大小，限制返回的历史记录数量。不传或小于等于0时返回所有历史记录
     * @return 聊天历史记录列表
     */
    protected List<String> getChatHistory(String key, int historyWindowSize) {
        List<String> history = chatHistory.getOrDefault(key, new ArrayList<>());
        if (history.size() <= historyWindowSize || historyWindowSize <= 0) {
            return new ArrayList<>(history);
        }
        return new ArrayList<>(history.subList(history.size() - historyWindowSize, history.size()));
    }
    
    /**
     * 获取所有聊天历史
     * @param key 聊天历史的键
     * @return 聊天历史记录列表
     */
    protected List<String> getChatHistory(String key) {
        return getChatHistory(key, 0);
    }
    
    /**
     * 清除所有聊天历史
     */
    protected void clearChatHistory() {
        chatHistory.clear();
    }
    
    @Override
    public Mono<Void> execute(WorkflowContext context) {
        return doExecute(context)
                .doFinally(signalType -> clearChatHistory());
    }
    
    /**
     * 子类实现此方法处理节点逻辑
     * @param context 工作流上下文
     * @return Mono<Void>
     */
    protected abstract Mono<Void> doExecute(WorkflowContext context);
} 