openapi: 3.0.0
info: {version: 1.0.8, title: RGW APIs For Owner Club}
servers:
- {description: '', url: 'https://isuzu-qa-gateway.stationdm.com/dev'}
paths:
  /user/deleteUser/v1:
    delete:
      tags: [user]
      summary: ''
      description: Delete user with specified email
      parameters:
      - name: knt-req-id
        in: header
        description: Request ID. This should be unique for each request.
        schema: {type: string, example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
        required: true
      - in: header
        name: knt-timestamp
        required: true
        description: The timestamp when the request is sent(milliseconds from 1970)
        schema: {type: string, example: '1522071940096'}
      - in: header
        name: knt-correlation-id
        required: true
        description: The device id of end point. For server, use IP instead
        schema: {type: string, example: 127.0.0.1}
      - in: header
        name: knt-app-key
        required: true
        description: The APP Code which is get from Kintaro Portal.
        schema: {type: string, example: '202406060358137316135'}
      - in: header
        name: knt-locale
        required: false
        description: The Locale of the client
        schema: {type: string, example: en-US}
      - in: header
        name: Authorization
        required: true
        description: Basic Authorization Token.
        schema: {type: string, example: Basic d1ZtVnJKSDA6ZWQwM2lWMkc=}
      - in: header
        name: knt-access-token
        required: false
        description: The access token to access IDM. This header is required if the API need to do Usher permission check
        schema: {type: string, example: 91D28437F82A4D028B45C98980B52951}
      - in: query
        name: email
        required: true
        description: The user email to be deleted
        schema: {type: string}
      responses:
        '200':
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    title: SuccessfulExample
                    properties:
                      payload:
                        type: object
                        description: The content data of the response. This field is required if state is 'S'
                        properties: {}
                      errorCode: {type: integer, example: 0}
                      state:
                        type: string
                        description: "The response status: S: success, F: fail"
                        example: S
                      statusCode: {type: integer, example: 200}
                  - $ref: '#/components/schemas/FailedExample'
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}

  /user/revokeInvitation/v1:
      post:
        tags: [user]
        summary: ''
        description: Revoke a pending invitation
        parameters:
        - name: knt-req-id
          in: header
          description: Request ID. This should be unique for each request.
          schema: {type: string, example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
          required: true
        - in: header
          name: knt-timestamp
          required: true
          description: The timestamp when the request is sent(milliseconds from 1970)
          schema: {type: string, example: '1522071940096'}
        - in: header
          name: knt-correlation-id
          required: true
          description: The device id of end point. For server, use IP instead
          schema: {type: string, example: 127.0.0.1}
        - in: header
          name: knt-app-key
          required: true
          description: The APP Code which is get from Kintaro Portal.
          schema: {type: string, example: '202406060358137316135'}
        - in: header
          name: knt-locale
          required: false
          description: The Locale of the client
          schema: {type: string, example: en-US}
        - in: header
          name: Authorization
          required: true
          description: Basic Authorization Token.
          schema: {type: string, example: Basic d1ZtVnJKSDA6ZWQwM2lWMkc=}
        - in: header
          name: content-type
          required: false
          description: The Content Type of the POST request. This header is required if the API is POST request.
          schema: {type: string, default: application/json}
        - in: header
          name: knt-access-token
          required: false
          description: The access token to access IDM. This header is required if the API need to do Usher permission check
          schema: {type: string, example: 91D28437F82A4D028B45C98980B52951}
        requestBody:
          content:
            application/json:
              schema:
                type: object
                required: [invitationId]
                properties:
                  invitationId: {type: string, description: The invitationId from sendInvitationEmail API response}
          description: ''
          required: true
        responses:
          '200':
            content:
              application/json:
                schema:
                  oneOf:
                    - type: object
                      title: SuccessfulExample
                      properties:
                        payload:
                          type: object
                          description: The content data of the response. This field is required if state is 'S'
                          properties: {}
                        errorCode: {type: integer, example: 0}
                        state:
                          type: string
                          description: "The response status: S: success, F: fail"
                          example: S
                        statusCode: {type: integer, example: 200}
                    - $ref: '#/components/schemas/FailedExample'
            description: Successful Response
            headers:
              Content-Type:
                description: Response Content Type
                schema: {type: string, default: application/json}

  /user/getInvitationStatus/v1:
      get:
        tags: [user]
        summary: ''
        description: Get the status of an invitation
        parameters:
        - name: knt-req-id
          in: header
          description: Request ID. This should be unique for each request.
          schema: {type: string, example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
          required: true
        - in: header
          name: knt-timestamp
          required: true
          description: The timestamp when the request is sent(milliseconds from 1970)
          schema: {type: string, example: '1522071940096'}
        - in: header
          name: knt-correlation-id
          required: true
          description: The device id of end point. For server, use IP instead
          schema: {type: string, example: 127.0.0.1}
        - in: header
          name: knt-app-key
          required: true
          description: The APP Code which is get from Kintaro Portal.
          schema: {type: string, example: '202406060358137316135'}
        - in: header
          name: knt-locale
          required: false
          description: The Locale of the client
          schema: {type: string, example: en-US}
        - in: header
          name: Authorization
          required: true
          description: Basic Authorization Token.
          schema: {type: string, example: Basic d1ZtVnJKSDA6ZWQwM2lWMkc=}
        - in: header
          name: knt-access-token
          required: false
          description: The access token to access IDM. This header is required if the API need to do Usher permission check
          schema: {type: string, example: 91D28437F82A4D028B45C98980B52951}
        - in: query
          name: invitationId
          required: true
          description: The invitationId from sendInvitationEmail API response
          schema: {type: string}
        responses:
          '200':
            content:
              application/json:
                schema:
                  oneOf:
                    - type: object
                      title: SuccessfulExample
                      properties:
                        payload:
                          type: object
                          description: The content data of the response. This field is required if state is 'S'
                          properties:
                            invitationStatus: {type: string, description: 'ACCEPTED | PENDING | REVOKED', example: ACCEPTED}
                        errorCode: {type: integer, example: 0}
                        state:
                          type: string
                          description: "The response status: S: success, F: fail"
                          example: S
                        statusCode: {type: integer, example: 200}
                    - $ref: '#/components/schemas/FailedExample'
            description: Successful Response
            headers:
              Content-Type:
                description: Response Content Type
                schema: {type: string, default: application/json}

  /user/getCompanyVehicleList/v1:
      get:
        tags: [user]
        summary: ''
        description: Get the list of vehicles associated with the user's company
        parameters:
        - name: knt-req-id
          in: header
          description: Request ID. This should be unique for each request.
          schema: {type: string, example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
          required: true
        - in: header
          name: knt-timestamp
          required: true
          description: The timestamp when the request is sent(milliseconds from 1970)
          schema: {type: string, example: '1522071940096'}
        - in: header
          name: knt-correlation-id
          required: true
          description: The device id of end point. For server, use IP instead
          schema: {type: string, example: 127.0.0.1}
        - in: header
          name: knt-app-key
          required: true
          description: The APP Code which is get from Kintaro Portal.
          schema: {type: string, example: '202406060358137316135'}
        - in: header
          name: knt-locale
          required: false
          description: The Locale of the client
          schema: {type: string, example: en-US}
        - in: header
          name: Authorization
          required: true
          description: Basic Authorization Token.
          schema: {type: string, example: Basic d1ZtVnJKSDA6ZWQwM2lWMkc=}
        - in: header
          name: knt-access-token
          required: false
          description: The access token to access IDM. This header is required if the API need to do Usher permission check
          schema: {type: string, example: 91D28437F82A4D028B45C98980B52951}
        responses:
          '200':
            content:
              application/json:
                schema:
                  oneOf:
                    - type: object
                      title: SuccessfulExample
                      properties:
                        payload:
                          type: object
                          description: The content data of the response. This field is required if state is 'S'
                          properties:
                            vehicleList:
                              type: array
                              description: The vehicles list of the user's company
                              items:
                                type: object
                                properties:
                                  bodyType: {type: string, description: ''}
                                  vehicleNickname: {type: string, description: '', example: my car}
                                  garageName: {type: string, description: '', example: Garage ABC}
                                  garageId: {type: string, description: '', example: '36'}
                                  garageState: {type: string, description: '', example: NY}
                                  garageAddress: {type: string, description: '', example: 123 Main St}
                                  addressId: {type: string, description: '', example: '85426'}
                                  servicingDealerCode: {type: string, description: '', example: abc456}
                                  bodyTypeId: {type: integer, description: '', format: int64}
                                  garageStreet2: {type: string, description: '', example: Suite 200}
                                  rgwGarageId: {type: integer, description: '', format: int64}
                                  garageStreet1: {type: string, description: '', example: 123 Main St}
                                  sellingDealerName: {type: string, description: '', example: Dealer XYZ}
                                  vin: {type: string, description: '', example: 54DE5W1V2SSER9902}
                                  garageZip: {type: string, description: '', example: '12345'}
                                  servicingDealerName: {type: string, description: '', example: Dealer XYZ}
                                  garageCity: {type: string, description: '', example: Anytown}
                                  sellingDealerCode: {type: string, description: '', example: abc123}
                        errorCode: {type: integer, example: 0}
                        state:
                          type: string
                          description: "The response status: S: success, F: fail"
                          example: S
                        statusCode: {type: integer, example: 200}
                    - $ref: '#/components/schemas/FailedExample'
            description: Successful Response
            headers:
              Content-Type:
                description: Response Content Type
                schema: {type: string, default: application/json}
  /vehicle/getGPSTac/v1:
    get:
      tags: [vehicle]
      summary: ''
      description: Get the content of GPS Terms & Conditions.Available for the vehicle which is Incentived by government.
      parameters:
      - name: knt-req-id
        in: header
        description: Request ID. This should be unique for
        schema: {type: string, example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
      - in: header
        name: knt-timestamp
        required: true
        description: The timestamp when the request is sent(milliseconds
            from 1970)
        schema: {type: string, example: '1522071940096'}
      - in: header
        name: knt-correlation-id
        description: 'The device id of end point. For server,
            use IP instead'
        schema: {type: string,  example: 127.0.0.1}
      - in: header
        name: knt-app-key
        required: true
        description: The APP Code which is get from RGW
            Portal.
        schema: {type: string,  example: '202406060358137316135'}
      - in: header
        name: knt-locale
        required: false
        description: The Locale of the client
        schema: {type: string, example: en-US}
      - in: header
        name: authorization
        required: true
        description: Authorization is the process of granting access to resources based on the user's identity and privileges
        schema: {type: string, example: Basic xxxxxxxx}
      responses:
        '200':
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    title: SuccessfulExample
                    properties:
                      payload:
                        type: object
                        description: |-
                          The content data of the response.
                          This field is required if state is ‘S’
                        properties:
                          data: {type: string, description: GPS TAC data}
                          version: {type: string, description: GPS TAC version No, example: 1.0.0}
                      state:
                        type: string
                        description: |-
                          The response status:
                          S: success
                          F: fail
                        example: S
                  - $ref: '#/components/schemas/FailedExample'
              examples:
                SuccessfulExample:
                  summary: Successful Example
                  value:
                    payload: {"data": "string","version": "1.0.0"}
                    state: S
                FailedExample:
                  summary: Failed Example
                  value:
                    errorForDisplay: This app does not exist.
                    errorCode: 400001
                    extraCode: 4001
                    extraMessage: IDM_session_expired
                    state: F
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}
        '503':
          description: Service Unavailable
  /user/getGatexSSOUrl/v1:
    get:
      tags: [user]
      summary: ''
      description: Get the Gatex SSO URL. This URL can redirect to Gatex portal with current user login state.
      parameters:
      - in: header
        name: knt-req-id
        description: Request ID. This should be unique for
            each request.
        schema: {type: string,  example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
      - in: header
        name: knt-timestamp
        required: true
        description: The timestamp when the request is sent(milliseconds
            from 1970)
        schema: {type: string,  example: '1522071940096'}
      - in: header
        name: knt-correlation-id
        required: false
        description: 'The device id of end point. For server,
            use IP instead'
        schema: {type: string, example: 127.0.0.1}
      - in: header
        name: knt-app-key
        required: true
        description: The APP Code which is get from RGW
            Portal.,
        schema: {type: string, example: '202406060358137316135'}
      - in: header
        name: knt-locale
        required: false
        description: The Locale of the client
        schema: {type: string,  example: en-US}
      - in: header
        name: authorization
        required: true
        description: Authorization is the process of granting access to resources based on the user's identity and privileges
        schema: {type: string, example: Basic xxxxxxxx}
      - in: header
        name: knt-access-token
        required: true
        description: |-
            The access token to access IDM.
            This header is required if the API need to do Usher permission check
        schema:
          type: string
          example: 91D28437F82A4D028B45C98980B52951
      responses:
        '200':
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    title: SuccessfulExample
                    properties:
                      payload:
                        type: object
                        description: |-
                          The content data of the response.
                          This field is required if state is ‘S’
                        properties:
                          ssoUrl: {type: string, description: sso url}
                      state:
                        type: string
                        description: |-
                          The response status:
                          S: success
                          F: fail
                        example: S
                  - type: object
                    title: FailedExample
                    properties:
                      errorForDisplay:
                        type: string
                        description: |-
                          The error message.
                          This field is required if state is ‘F’.
                        example: Logged out for timeout.
                      extraCode: {type: string, description: The error code return from
                          3rd service provider, example: IDM_session_expired}
                      extraMessage: {type: string, description: The error message return
                          from 3rd service provider., example: IDM_session_expired}
                      errorCode:
                        oneOf:
                          - type: integer
                            title: BusinessLogicErrors
                            description: |
                                The error code. This field is required if the state is 'F'. Specific error codes will be defined as development progresses.
                            example: '920000'
                          - $ref: '#/components/schemas/CommonErrorCode'
                        maxLength: 6
                      state:
                        type: string
                        description: |-
                          The response status:
                          S: success
                          F: failed
                        example: F
              examples:
                SuccessfulExample:
                  summary: Successful Example
                  value:
                    payload: {"ssoUrl": "string"}
                    state: S
                FailedExample:
                  summary: Failed Example
                  value:
                    errorForDisplay: This app does not exist.
                    errorCode: 400001
                    extraCode: 4001
                    extraMessage: IDM_session_expired
                    state: F
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}
        '503':
          description: Service Unavailable
  /user/getProfileInfo/v1:
    get:
      tags: [user]
      summary: ''
      description: Get the user profile information.
      parameters:
      - in: header
        name: knt-req-id
        description: Request ID. This should be unique for
            each request.
        schema: {type: string, example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
      - in: header
        name: knt-timestamp
        required: true
        description: The timestamp when the request is sent(milliseconds
            from 1970)
        schema: {type: string, example: '1522071940096'}
      - in: header
        name: knt-correlation-id
        required: false
        description: 'The device id of end point. For server,
            use IP instead'
        schema: {type: string, example: 127.0.0.1}
      - in: header
        name: knt-app-key
        required: true
        description: The APP Code which is get from RGW
            Portal.
        schema: {type: string, example: '202406060358137316135'}
      - in: header
        name: knt-locale
        required: false
        description: The Locale of the client
        schema: {type: string, example: en-US}
      - in: header
        name: authorization
        required: true
        description: Authorization is the process of granting access to resources based on the user's identity and privileges
        schema: {type: string, example: Basic xxxxxxxx}
      - in: header
        name: knt-access-token
        required: true
        description: |-
            The access token to access IDM.
            This header is required if the API need to do Usher permission check
        schema:
          type: string
          example: 91D28437F82A4D028B45C98980B52951
      responses:
        '200':
          content:
            application/json:
              schema:
                  oneOf:
                    - type: object
                      title: SuccessfulExample
                      properties:
                        payload:
                          type: object
                          properties:
                            rgwId: {type: string, description: '', example: 40bae3def506417ab197f96e4a55d9ab}
                            userAddressId: {type: string, description: 'address id for user', example: 4567}
                            country: {type: string, description: '', example: Myanmar}
                            lastName: {type: string, description: '', example: Bernier}
                            gender: {type: integer, description: '', example: '2', format: int64}
                            street3: {type: string, description: '', example: Kaley Park}
                            city: {type: string, description: '', example: Crooksside}
                            latestTacData: {type: string, description: '', example: xxx}
                            timezone: {type: string, description: '', example: America/Los_Angeles}
                            companyName: {type: string, description: '', example: SY-Customer-05}
                            language: {type: string, description: '', example: en}
                            street1: {type: string, description: '', example: Fahey Parkway}
                            state: {type: string, description: '', example: CA}
                            street2: {type: string, description: '', example: Jack Centers}
                            latestTacVersion: {type: string, description: '', example: xxx}
                            email: {type: string, description: '', example: <EMAIL>}
                            adminFlag: {type: string, description: 'admin flag', example: 'true'}
                            defaultServicingDealerName: {type: string, description: 'default servicing dealer name', example: 'Dealer ABC'}
                            defaultServicingDealerCode: {type: string, description: 'default servicing dealer code', example: 'D12345'}
                            businessType: {type: string, description: 'business type', example: '0'}
                            rgwCompanyId: {type: integer, description: 'rgw company id', example: 12}
                            vehicleDetails:
                              type: array
                              description: ''
                              items:
                                type: object
                                properties:
                                  vin: {type: string, description: 'vin code', example: 54DE5W1V2SSER9902}
                                  garageName: {type: string, description: '', example: Garage
                                      ABC}
                                  garageId: {type: integer, description: '', example: '36',
                                    format: int64}
                                  garageStreet2: {type: string, description: '', example: Suite
                                      200}
                                  garageStreet1: {type: string, description: '', example: 123
                                      Main St}
                                  sellingDealerName: {type: string, description: '', example: Dealer
                                      XYZ}
                                  garageZip: {type: string, description: '', example: '12345'}
                                  servicingDealerName: {type: string, description: '', example: Dealer
                                      XYZ}
                                  garageState: {type: string, description: '', example: NY}
                                  garageCity: {type: string, description: '', example: Anytown}
                                  garageAddress: {type: string, description: '', example: 123
                                      Main St}
                                  addressId: {type: string, description: Address Number,
                                  example: 87856}
                                  sellingDealerCode: {type: string, description: Code of the selling dealer,
                                    example: ABC123}
                                  servicingDealerCode: {type: string, description: Code of the servicing dealer,
                                    example: ABC456}
                                  vehicleNickname: {type: string, description: Vehicle Nickname,
                                    example: My Car}
                                  bodyType: {type: string, description: body type,
                                    example: string}
                                  rgwGarageId: {type: integer, description: rgw garage id,
                                    example: string}
                                  bodyTypeId: {type: integer, description: body type id,
                                    example: string}
                                  vehicleName: { type: string, description: 'Vehicle name', example: 'Corolla' }
                                  vehicleNumber: { type: string, description: 'Vehicle number', example: 'VN12345' }
                            mobile: {type: string, description: '', example: ************}
                            receiveEmail: {type: string, description: '', example: '1'}
                            zipcode: {type: string, description: '', example: '985211'}
                            firstName: {type: string, description: '', example: Jeff}
                            companyId: {type: integer, description: '', example: '39', format: int64}
                            gatexId: {type: string, description: '', example: RWUSE0000061}
                            companyOwned: {type: string, description: '', example: '0'}
                            tacUpgradeFlag: {type: boolean, description: '', example: 'false'}
                            defaultGarage:
                              type: object
                              properties:
                                garageName: {type: string, description: '', example: Garage ABC}
                                garageStreet2: {type: string, description: '', example: Suite 200}
                                garageStreet1: {type: string, description: '', example: 123 Main St}
                                garageZip: {type: string, description: '', example: 90802}
                                garageState: {type: string, description: '', example: NY}
                                garageCity: {type: string, description: '', example: Anytown}
                                garageAddress: {type: string, description: '', example: 123 Main St}
                        state:
                          type: string
                          description: |-
                            The response status:
                            S: success
                            F: fail
                          example: S
                        
                    - $ref: '#/components/schemas/FailedExample'
              examples:
                SuccessfulExample:
                  summary: Successful Example
                  value:
                    payload: {"rgwId":"40bae3def506417ab197f96e4a55d9ab","userAddressId":"4567","firstName":"Jeff","lastName":"Bernier","country":"Myanmar","gender":2,"mobile":"************","zipcode":"985211","city":"Crooksside","street1":"Fahey Parkway","street2":"Jack Centers","street3":"Kaley Park","state":"CA","email":"<EMAIL>","companyId":39,"gatexId":"RWUSE0000061","companyName":"SY-Customer-05","companyOwned":"0","receiveEmail":"1","timezone":"America/Los_Angeles","language":"en","adminFlag":"true","defaultServicingDealerName":"Dealer ABC","defaultServicingDealerCode":"D12345","businessType":"0","rgwCompanyId":12,"vehicleDetails":[{"vin":"54DE5W1V2SSER9902","garageName":"Garage ABC","garageId":36,"garageAddress":"123 Main St","garageStreet1":"123 Main St","garageStreet2":"Suite 200","garageCity":"Anytown","garageState":"NY","garageZip":"12345","sellingDealerName":"Dealer XYZ","servicingDealerName":"Dealer XYZ","addressId":"85426","sellingDealerCode":"abc123","servicingDealerCode":"abc456","vehicleNickname":"my car","bodyType":"string","rgwGarageId":16,"bodyTypeId":14,"vehicleName":"Corolla","vehicleNumber":"VN12345"},{"vin":"54DE5W1V2SSER9903","garageName":"Garage GHI","garageId":35,"garageAddress":"789 Oak Dr","garageStreet1":"789 Oak Dr","garageStreet2":"Suite 400","garageCity":"Elsewhere","garageState":"TX","garageZip":"11223","sellingDealerName":"Dealer OPQ","servicingDealerName":"Dealer OPQ","addressId":"85427","sellingDealerCode":"abc567","servicingDealerCode":"abc789","vehicleNickname":"car sample","bodyType":"string","rgwGarageId":17,"bodyTypeId":13,"vehicleName":"Camry","vehicleNumber":"VN67890"}],"latestTacVersion":"1","latestTacData":"terms1","tacUpgradeFlag":false,"defaultGarage":{"garageName":"Garage ABC","garageStreet2":"Suite 200","garageStreet1":"123 Main St","garageZip":"90802","garageState":"NY","garageCity":"Anytown","garageAddress":"123 Main St"}}
                    state: S
                    
                FailedExample:
                  summary: Failed Example
                  value:
                    errorForDisplay: This app does not exist.
                    errorCode: 400001
                    extraCode: 4001
                    extraMessage: IDM_session_expired
                    state: F
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}
        '503':
          description: Service Unavailable
  /user/getUserTac/v1:
    get:
      tags: [user]
      summary: ''
      description: Get the content of user Terms & Conditions.
      parameters:
      - in: header
        name: knt-req-id
        required: false
        description: Request ID. This should be unique for
            each request.
        schema: {type: string,  example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
      - in: header
        name: knt-timestamp
        required: true
        description: The timestamp when the request is sent(milliseconds
            from 1970)
        schema: {type: string,  example: '1522071940096'}
      - in: header
        name: knt-correlation-id
        required: false
        description: 'The device id of end point. For server,
            use IP instead'
        schema: {type: string,  example: 127.0.0.1}
      - in: header
        name: knt-app-key
        required: true
        description: The APP Code which is get from RGW
            Portal.
        schema: {type: string,  example: '202406060358137316135'}
      - in: header
        name: knt-locale
        required: false
        description: The Locale of the client
        schema: {type: string,  example: en-US}
      - in: header
        name: authorization
        required: true
        description: Authorization is the process of granting access to resources based on the user's identity and privileges
        schema: {type: string, example: Basic xxxxxxxx}
      responses:
        '200':
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    title: SuccessfulExample
                    properties:
                      payload:
                        type: object
                        description: |-
                          The content data of the response.
                          This field is required if state is ‘S’
                        properties:
                          data: {type: string, description: TAC data}
                          version: {type: string, description: TAC version No, example: 1.0.0}
                      
                      state:
                        type: string
                        description: |-
                          The response status:
                          S: success
                          F: fail
                        example: S
                      
                  - $ref: '#/components/schemas/FailedExample'
              examples:
                SuccessfulExample:
                  summary: Successful Example
                  value:
                    payload: {"data":"string","version":"1.0.0"}
                    
                    state: S
                    
                FailedExample:
                  summary: Failed Example
                  value:
                    errorForDisplay: This app does not exist.
                    errorCode: 400001
                    extraCode: 4001
                    extraMessage: IDM_session_expired
                    state: F
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}
        '503':
          description: Service Unavailable
  /vehicle/linkVin/v1:
    post:
      tags: [vehicle]
      summary: ''
      description: Link the VIN to the current user.
      parameters:
      - in: header
        name: knt-req-id
        required: false
        description: Request ID. This should be unique for
            each request.
        schema: {type: string,  example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
      - in: header
        name: knt-timestamp
        required: true
        description: The timestamp when the request is sent(milliseconds
            from 1970)
        schema: {type: string, example: '1522071940096'}
      - in: header
        name: knt-correlation-id
        required: false
        description: 'The device id of end point. For server,
            use IP instead'
        schema: {type: string,  example: 127.0.0.1}
      - in: header
        name: knt-app-key
        required: true
        description: The APP Code which is get from RGW
            Portal.
        schema: {type: string,  example: '202406060358137316135'}
      - in: header
        name: knt-locale
        required: false
        description: The Locale of the client
        schema: {type: string,  example: en-US}
      - in: header
        name: content-type
        required: false
        schema:
          type: string
          description: |-
            The Content Type of the POST request.
            This header is required if the API is POST request.
          default: application/json
      - in: header
        name: authorization
        required: true
        description: Authorization is the process of granting access to resources based on the user's identity and privileges
        schema: {type: string, example: Basic xxxxxxxx}
      - in: header
        name: knt-access-token
        required: true
        description: |-
            The access token to access IDM.
            This header is required if the API need to do Usher permission check
        schema:
          type: string
          example: 91D28437F82A4D028B45C98980B52951
      responses:
        '200':
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    title: SuccessfulExample
                    properties:
                      payload:
                        type: object
                        description: |-
                          The content data of the response.
                          This field is required if state is ‘S’
                        properties: {}
                      
                      state:
                        type: string
                        description: |-
                          The response status:
                          S: success
                          F: fail
                        example: S
                      
                  - $ref: '#/components/schemas/FailedExample'
              examples:
                SuccessfulExample:
                  summary: Successful Example
                  value:
                    payload: {}
                    
                    state: S
                    
                FailedExample:
                  summary: Failed Example
                  value:
                    errorForDisplay: This app does not exist.
                    errorCode: 400001
                    extraCode: 4001
                    extraMessage: IDM_session_expired
                    state: F
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}
        '503':
          description: Service Unavailable
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                vin:
                  type: string
                  description: VIN number
                  example: 1HGCM82633A123456
                garageName:
                  type: string
                  description: Name of the garage
                  example: Garage ABC
                garageAddress:
                  type: string
                  description: Address of the garage
                  example: 123 Main St
                garageStreet1:
                  type: string
                  description: Street line 1 of the garage address
                  example: 123 Main St
                garageStreet2:
                  type: string
                  description: Street line 2 of the garage address
                  example: Suite 200
                garageCity:
                  type: string
                  description: City of the garage
                  example: Anytown
                garageState:
                  type: string
                  description: State of the garage
                  example: NY
                garageZip:
                  type: string
                  description: Zip code of the garage
                  example: 12345
                sellingDealerName:
                  type: string
                  description: Name of the selling dealer
                  example: Dealer XYZ
                sellingDealerCode:
                  type: string
                  description: Code of the selling dealer
                  example: abc123
                servicingDealerName:
                  type: string
                  description: Name of the servicing dealer
                  example: Dealer XYZ
                servicingDealerCode:
                  type: string
                  description: Code of the servicing dealer
                  example: abc456
                addressId:
                  type: string
                  description: Address Number
                  example: 87856
                vehicleNickname:
                  type: string
                  description: Vehicle Nickname
                  example: My Car
                vehicleName:
                  type: string
                  description: Vehicle Name
                  example: isuzu truck
                vehicleNumber:
                  type: string
                  description: Plate Number
                  example: PN12345
                bodyType:
                  type: string
                  description: body type
                  example: String
                bodyTypeId:
                  type: integer
                  description: body type id
                  example: 12
              required:
                - vin
                - garageName
                - garageAddress
                - garageStreet1
                - garageCity
                - garageState
                - garageZip
                - sellingDealerName
                - sellingDealerCode
                - servicingDealerName
                - servicingDealerCode
                - addressId
        description: ''
        required: true
  /vehicle/turnOnGPS/v1:
    post:
      tags: [vehicle]
      summary: ''
      description: 'Turn on GPS of the vehicle.'
      parameters:
      - in: header
        name: knt-req-id
        required: false
        description: Request ID. This should be unique for
            each request.
        schema: {type: string,  example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
      - in: header
        name: knt-timestamp
        required: true
        description: The timestamp when the request is sent(milliseconds
            from 1970)
        schema: {type: string,  example: '1522071940096'}
      - in: header
        name: knt-correlation-id
        required: false
        description: 'The device id of end point. For server,
            use IP instead'
        schema: {type: string,  example: 127.0.0.1}
      - in: header
        name: knt-app-key
        required: true
        description: The APP Code which is get from RGW
            Portal.
        schema: {type: string,  example: '202406060358137316135'}
      - in: header
        name: knt-locale
        required: false
        description: The Locale of the client
        schema: {type: string,  example: en-US}
      - in: header
        name: content-type
        required: false
        schema:
          type: string
          description: |-
            The Content Type of the POST request.
            This header is required if the API is POST request.
          default: application/json
      - in: header
        name: authorization
        required: true
        description: Authorization is the process of granting access to resources based on the user's identity and privileges
        schema: {type: string, example: Basic xxxxxxxx}
      - in: header
        name: knt-access-token
        required: true
        description: |-
            The access token to access IDM.
            This header is required if the API need to do Usher permission check
        schema:
          type: string
          example: 91D28437F82A4D028B45C98980B52951
      responses:
        '200':
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    title: SuccessfulExample
                    properties:
                      payload:
                        type: object
                        description: |-
                          The content data of the response.
                          This field is required if state is ‘S’
                        properties: {}
                      
                      state:
                        type: string
                        description: |-
                          The response status:
                          S: success
                          F: fail
                        example: S
                      
                  - $ref: '#/components/schemas/FailedExample'
              examples:
                SuccessfulExample:
                  summary: Successful Example
                  value:
                    payload: {}
                    
                    state: S
                    
                FailedExample:
                  summary: Failed Example
                  value:
                    errorForDisplay: This app does not exist.
                    errorCode: 400001
                    extraCode: 4001
                    extraMessage: IDM_session_expired
                    state: F
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}
        '503':
          description: Service Unavailable
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [version]
              properties:
                version: {type: string, description: GSP TAC version NO, example: 1.0.0}
                vin: {type: string, description: vin , example: XXX111122}
        description: ''
        required: true
  /user/acceptUserTac/v1:
    post:
      tags: [user]
      summary: ''
      description: Accept the user Terms & Conditions.
      parameters:
      - in: header
        name: knt-req-id
        required: false
        description: Request ID. This should be unique for
            each request.
        schema: {type: string,  example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
      - in: header
        name: knt-timestamp
        required: true
        description: The timestamp when the request is sent(milliseconds
            from 1970)
        schema: {type: string,  example: '1522071940096'}
      - in: header
        name: knt-correlation-id
        required: false
        description: 'The device id of end point. For server,
            use IP instead'
        schema: {type: string,  example: 127.0.0.1}
      - in: header
        name: knt-app-key
        required: true
        description: The APP Code which is get from RGW
            Portal.
        schema: {type: string,  example: '202406060358137316135'}
      - in: header
        name: knt-locale
        required: false
        description: The Locale of the client
        schema: {type: string,  example: en-US}
      - in: header
        name: content-type
        required: false
        schema:
          type: string
          description: |-
            The Content Type of the POST request.
            This header is required if the API is POST request.
          default: application/json
      - in: header
        name: authorization
        required: true
        description: Authorization is the process of granting access to resources based on the user's identity and privileges
        schema: {type: string, example: Basic xxxxxxxx}
      - in: header
        name: knt-access-token
        required: true
        description: |-
            The access token to access IDM.
            This header is required if the API need to do Usher permission check
        schema:
          type: string
          example: 91D28437F82A4D028B45C98980B52951
      responses:
        '200':
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    title: SuccessfulExample
                    properties:
                      payload:
                        type: object
                        description: |-
                          The content data of the response.
                          This field is required if state is ‘S’
                        properties: {}
                      
                      state:
                        type: string
                        description: |-
                          The response status:
                          S: success
                          F: fail
                        example: S
                      
                  - $ref: '#/components/schemas/FailedExample'
              examples:
                SuccessfulExample:
                  summary: Successful Example
                  value:
                    payload: {}
                    
                    state: S
                    
                FailedExample:
                  summary: Failed Example
                  value:
                    errorForDisplay: This app does not exist.
                    errorCode: 400001
                    extraCode: 4001
                    extraMessage: IDM_session_expired
                    state: F
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}
        '503':
          description: Service Unavailable
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                version: {type: string, description: TAC version No, example: 1.0.1}
        description: ''
        required: true
  /user/sendInvitationEmail/v1:
    post:
      tags: [user]
      summary: ''
      parameters:
      - in: header
        name: knt-req-id
        required: false
        description: Request ID. This should be unique for
            each request.
        schema: {type: string,  example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
      - in: header
        name: knt-timestamp
        required: true
        description: The timestamp when the request is sent(milliseconds
            from 1970)
        schema: {type: string,  example: '1522071940096'}
      - in: header
        name: knt-correlation-id
        required: false
        description: 'The device id of end point. For server,
            use IP instead'
        schema: {type: string,  example: 127.0.0.1}
      - in: header
        name: knt-app-key
        required: true
        description: The APP Code which is get from RGW
            Portal.
        schema: {type: string,  example: '202406060358137316135'}
      - in: header
        name: knt-locale
        required: false
        description: The Locale of the client
        schema: {type: string,  example: en-US}
      - in: header
        name: content-type
        required: false
        schema:
          type: string
          description: |-
            The Content Type of the POST request.
            This header is required if the API is POST request.
          default: application/json
      - in: header
        name: authorization
        required: true
        description: Authorization is the process of granting access to resources based on the user's identity and privileges
        schema: {type: string, example: Basic xxxxxxxx}
      - in: header
        name: knt-access-token
        required: true
        description: |-
            The access token to access IDM.
            This header is required if the API need to do Usher permission check
        schema:
          type: string
          example: 91D28437F82A4D028B45C98980B52951
      responses:
        '200':
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    title: SuccessfulExample
                    properties:
                      payload:
                        type: object
                        properties:
                          invitationId: 
                            type: string
                            description: ''
                            example: string
                      state:
                        type: string
                        description: |-
                          The response status:
                          S: success
                          F: fail
                        example: S
                      
                  - $ref: '#/components/schemas/FailedExample'
              examples:
                SuccessfulExample:
                  summary: Successful Example
                  value:
                    payload: {"invitationId":"eviusynnhgxxx"}
                    
                    state: S
                    
                FailedExample:
                  summary: Failed Example
                  value:
                    errorForDisplay: This app does not exist.
                    errorCode: 400001
                    extraCode: 4001
                    extraMessage: IDM_session_expired
                    state: F
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}
        '503':
          description: Service Unavailable
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [email]
              properties:
                rgwGarageId: {type: integer, description: rgw garage id, example: '12', format: int64}
                email: {type: string, description: invitee email, example: <EMAIL>}
                garageInfo:
                  type: object
                  properties:
                    garageName: {type: string, description: '', example: Garage ABC}
                    garageStreet2: {type: string, description: '', example: Suite 200}
                    garageStreet1: {type: string, description: '', example: 123 Main St}
                    garageZip: {type: string, description: '', example: 90802}
                    garageState: {type: string, description: '', example: NY}
                    garageCity: {type: string, description: '', example: Anytown}
                    garageAddress: {type: string, description: '', example: 123 Main St}
        description: ''
        required: true
  /user/transferAdminPermission/v1:
    post:
      tags: [user]
      summary: ''
      parameters:
      - in: header
        name: knt-req-id
        required: false
        description: Request ID. This should be unique for
            each request.
        schema: {type: string,  example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
      - in: header
        name: knt-timestamp
        required: true
        description: The timestamp when the request is sent(milliseconds
            from 1970)
        schema: {type: string,  example: '1522071940096'}
      - in: header
        name: knt-correlation-id
        required: false
        description: 'The device id of end point. For server,
            use IP instead'
        schema: {type: string,  example: 127.0.0.1}
      - in: header
        name: knt-app-key
        required: true
        description: The APP Code which is get from RGW
            Portal.
        schema: {type: string,  example: '202406060358137316135'}
      - in: header
        name: knt-locale
        required: false
        description: The Locale of the client
        schema: {type: string,  example: en-US}
      - in: header
        name: content-type
        required: false
        schema:
          type: string
          description: |-
            The Content Type of the POST request.
            This header is required if the API is POST request.
          default: application/json
      - in: header
        name: authorization
        required: true
        description: Authorization is the process of granting access to resources based on the user's identity and privileges
        schema: {type: string, example: Basic xxxxxxxx}
      - in: header
        name: knt-access-token
        required: true
        description: |-
            The access token to access IDM.
            This header is required if the API need to do Usher permission check
        schema:
          type: string
          example: 91D28437F82A4D028B45C98980B52951
      responses:
        '200':
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    title: SuccessfulExample
                    properties:
                      payload:
                        type: object
                        description: |-
                          The content data of the response.
                          This field is required if state is ‘S’
                        properties: {}
                      
                      state:
                        type: string
                        description: |-
                          The response status:
                          S: success
                          F: fail
                        example: S
                      
                  - $ref: '#/components/schemas/FailedExample'
              examples:
                SuccessfulExample:
                  summary: Successful Example
                  value:
                    payload: {}
                    
                    state: S
                    
                FailedExample:
                  summary: Failed Example
                  value:
                    errorForDisplay: This app does not exist.
                    errorCode: 400001
                    extraCode: 4001
                    extraMessage: IDM_session_expired
                    state: F
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}
        '503':
          description: Service Unavailable
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [email]
              properties:
                email: {type: string, description: target user email, example: <EMAIL>}
        description: ''
        required: true
  /user/verifyCompanyAuth/v1:
    post:
      tags: [user]
      summary: ''
      parameters:
      - in: header
        name: knt-req-id
        required: false
        description: Request ID. This should be unique for
            each request.
        schema: {type: string,  example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
      - in: header
        name: knt-timestamp
        required: true
        description: The timestamp when the request is sent(milliseconds
            from 1970)
        schema: {type: string,  example: '1522071940096'}
      - in: header
        name: knt-correlation-id
        required: false
        description: 'The device id of end point. For server,
            use IP instead'
        schema: {type: string,  example: 127.0.0.1}
      - in: header
        name: knt-app-key
        required: true
        description: The APP Code which is get from RGW
            Portal.
        schema: {type: string,  example: '202406060358137316135'}
      - in: header
        name: knt-locale
        required: false
        description: The Locale of the client
        schema: {type: string,  example: en-US}
      - in: header
        name: content-type
        required: false
        schema:
          type: string
          description: |-
            The Content Type of the POST request.
            This header is required if the API is POST request.
          default: application/json
      - in: header
        name: authorization
        required: true
        description: Authorization is the process of granting access to resources based on the user's identity and privileges
        schema: {type: string, example: Basic xxxxxxxx}
      responses:
        '200':
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    title: SuccessfulExample
                    properties:
                      payload:
                        type: object
                        properties:
                          firstName:
                            type: string
                            description: first name
                            example: John
                          lastName:
                            type: string
                            description: last name
                            example: Doe
                          companyId:
                            type: integer
                            description: company id
                            example: 2001
                            format: int64
                          garageName:
                            type: string
                            description: garage name
                            example: Central Garage
                          companyName:
                            type: string
                            description: company name
                            example: Doe Enterprises
                          garageId:
                            type: integer
                            description: garage id
                            example: 1001
                            format: int64
                          adminFlag:
                            type: boolean
                            description: is admin
                            example: true
                          email:
                            type: string
                            description: email
                            example: <EMAIL>
                          garageAddress:
                            type: string
                            description: garage address
                            example: 123 Main St, Springfield
                          receiverEmail:
                            type: string
                            description: receiver email
                            example: <EMAIL>
                          rgwCompanyId:
                            type: integer
                            description: rgw company id
                            example: 11
                            format: int64
                          rgwGarageId:
                            type: integer
                            description: rgw garage id
                            example: 12
                            format: int64
                      state:
                        type: string
                        description: |-
                          The response status:
                          S: success
                          F: fail
                        example: S
                      
                  - $ref: '#/components/schemas/FailedExample'
              examples:
                SuccessfulExample:
                  summary: Successful Example
                  value:
                    payload: {"firstName":"John","lastName":"Doe","companyId":2001,"garageName":"Central Garage","companyName":"Doe Enterprises","garageId":1001,"adminFlag":true,"email":"<EMAIL>","garageAddress":"123 Main St, Springfield","receiverEmail":"<EMAIL>","rgwCompanyId":11,"rgwGarageId":12}
                    state: S
                FailedExample:
                  summary: Failed Example
                  value:
                    errorForDisplay: This app does not exist.
                    errorCode: 400001
                    extraCode: 4001
                    extraMessage: IDM_session_expired
                    state: F
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}
        '503':
          description: Service Unavailable
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [invitationCode]
              properties:
                invitationCode: {type: string}
        description: ''
        required: true
  /user/getCompanyUserList/v1:
    get:
      tags: [user]
      summary: ''
      parameters:
      - in: header
        name: knt-req-id
        description: Request ID. This should be unique for
            each request.
        schema: {type: string, example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
      - in: header
        name: knt-timestamp
        required: true
        description: The timestamp when the request is sent(milliseconds
            from 1970)
        schema: {type: string, example: '1522071940096'}
      - in: header
        name: knt-correlation-id
        required: false
        description: 'The device id of end point. For server,
            use IP instead'
        schema: {type: string, example: 127.0.0.1}
      - in: header
        name: knt-app-key
        required: true
        description: The APP Code which is get from RGW
            Portal.
        schema: {type: string, example: '202406060358137316135'}
      - in: header
        name: knt-locale
        required: false
        description: The Locale of the client
        schema: {type: string, example: en-US}
      - in: header
        name: authorization
        required: true
        description: Authorization is the process of granting access to resources based on the user's identity and privileges
        schema: {type: string, example: Basic xxxxxxxx}
      - in: header
        name: knt-access-token
        required: true
        description: |-
            The access token to access IDM.
            This header is required if the API need to do Usher permission check
        schema:
          type: string
          example: 91D28437F82A4D028B45C98980B52951
      responses:
        '200':
          content:
            application/json:
              schema:
                  oneOf:
                    - type: object
                      title: SuccessfulExample
                      properties:
                        payload:
                          type: object
                          properties:
                            branchList:
                              type: array
                              description: ''
                              items:
                                type: object
                                properties:
                                  branchId: 
                                    type: integer
                                    description: ''
                                    example: 101
                                    format: int64
                                  branchName: 
                                    type: string
                                    description: ''
                                    example: Main Branch
                                  branchAddressInfo: 
                                    type: string
                                    description: ''
                                    example: 123 Main St, Springfield
                                  rgwGarageId: 
                                    type: integer
                                    description: ''
                                    example: 11
                                    format: int64
                            userList:
                              type: array
                              description: ''
                              items:
                                type: object
                                properties:
                                  branchId: 
                                    type: integer
                                    description: ''
                                    example: 101
                                    format: int64
                                  lastName: 
                                    type: string
                                    description: ''
                                    example: Doe
                                  firstName: 
                                    type: string
                                    description: ''
                                    example: John
                                  branchName: 
                                    type: string
                                    description: ''
                                    example: Main Branch
                                  adminFlag: 
                                    type: boolean
                                    description: ''
                                    example: true
                                  email: 
                                    type: string
                                    description: ''
                                    example: <EMAIL>
                                  rgwCompanyId: 
                                    type: integer
                                    description: ''
                                    example: 10
                                    format: int64
                                  rgwGarageId: 
                                    type: integer
                                    description: ''
                                    example: 11
                                    format: int64
                            invitationList:
                              type: array
                              description: ''
                              items:
                                type: object
                                properties:
                                  email: 
                                    type: string
                                    description: ''
                                    example: <EMAIL>
                                  invitationId: 
                                    type: string
                                    description: ''
                                    example: string
                        state:
                          type: string
                          description: |-
                            The response status:
                            S: success
                            F: fail
                          example: S
                        
                    - $ref: '#/components/schemas/FailedExample'
              examples:
                SuccessfulExample:
                  summary: Successful Example
                  value:
                    payload: {"branchList":[{"branchId":101,"branchName":"Main Branch","branchAddressInfo":"123 Main St, Springfield","rgwGarageId":10}],"userList":[{"branchId":101,"lastName":"Doe","firstName":"John","branchName":"Main Branch","adminFlag":true,"email":"<EMAIL>","rgwCompanyId":11,"rgwGarageId":10}],"invitationList":[{"email":"<EMAIL>","invitationId":"eviusynnhgxxx"}]}
                    state: S
                FailedExample:
                  summary: Failed Example
                  value:
                    errorForDisplay: This app does not exist.
                    errorCode: 400001
                    extraCode: 4001
                    extraMessage: IDM_session_expired
                    state: F
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}
        '503':
          description: Service Unavailable
  /vehicle/verifyGatexVinCode/v1:
    post:
      tags: [vehicle]
      summary: ''
      description: Verify the vehicle's auth code. This auth code is sent to the vehicle owner via email.
      parameters:
      - in: header
        name: knt-req-id
        required: false
        description: Request ID. This should be unique for
            each request.
        schema: {type: string,  example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
      - in: header
        name: knt-timestamp
        required: true
        description: The timestamp when the request is sent(milliseconds
            from 1970)
        schema: {type: string,  example: '1522071940096'}
      - in: header
        name: knt-correlation-id
        required: false
        description: 'The device id of end point. For server,
            use IP instead'
        schema: {type: string,  example: 127.0.0.1}
      - in: header
        name: knt-app-key
        required: true
        description: The APP Code which is get from RGW
            Portal.
        schema: {type: string,  example: '202406060358137316135'}
      - in: header
        name: knt-locale
        required: false
        description: The Locale of the client
        schema: {type: string,  example: en-US}
      - in: header
        name: content-type
        required: false
        schema:
          type: string
          description: |-
            The Content Type of the POST request.
            This header is required if the API is POST request.
          default: application/json
      - in: header
        name: authorization
        required: true
        description: Authorization is the process of granting access to resources based on the user's identity and privileges
        schema: {type: string, example: Basic xxxxxxxx}
      - in: header
        name: knt-access-token
        required: true
        description: |-
            The access token to access IDM.
            This header is required if the API need to do Usher permission check
        schema:
          type: string
          example: 91D28437F82A4D028B45C98980B52951
      responses:
        '200':
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    title: SuccessfulExample
                    properties:
                      payload:
                        type: object
                        description: |-
                          The content data of the response.
                          This field is required if state is ‘S’
                        properties: {}
                      
                      state:
                        type: string
                        description: |-
                          The response status:
                          S: success
                          F: fail
                        example: S
                      
                  - $ref: '#/components/schemas/FailedExample'
              examples:
                SuccessfulExample:
                  summary: Successful Example
                  value:
                    payload: {}
                    
                    state: S
                    
                FailedExample:
                  summary: Failed Example
                  value:
                    errorForDisplay: This app does not exist.
                    errorCode: 400001
                    extraCode: 4001
                    extraMessage: IDM_session_expired
                    state: F
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}
        '503':
          description: Service Unavailable
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [code, vin]
              properties:
                code: {type: string, description: auth code from GateX, example: '123456'}
                vin: {type: string, description: vin number, example: XBG672}
        description: ''
        required: true

  /vehicle/batchUpdateVinInfo/v1:
    put:
      tags: [vehicle]
      summary: ''
      parameters:
        - in: header
          name: knt-req-id
          required: false
          description: Request ID. This should be unique for
              each request.
          schema: {type: string,  example: 1d04d89e-9f15-4c37-8832-1cb261b2c96d}
        - in: header
          name: knt-timestamp
          required: true
          description: The timestamp when the request is sent(milliseconds
              from 1970)
          schema: {type: string, example: '1522071940096'}
        - in: header
          name: knt-correlation-id
          required: false
          description: 'The device id of end point. For server,
              use IP instead'
          schema: {type: string,  example: 127.0.0.1}
        - in: header
          name: knt-app-key
          required: true
          description: The APP Code which is get from RGW
              Portal.
          schema: {type: string,  example: '202406060358137316135'}
        - in: header
          name: knt-locale
          required: false
          description: The Locale of the client
          schema: {type: string,  example: en-US}
        - in: header
          name: content-type
          required: false
          schema:
            type: string
            description: |-
              The Content Type of the POST request.
              This header is required if the API is POST request.
            default: application/json
        - in: header
          name: authorization
          required: true
          description: Authorization is the process of granting access to resources based on the user's identity and privileges
          schema: {type: string, example: Basic xxxxxxxx}
        - in: header
          name: knt-access-token
          required: true
          description: |-
              The access token to access IDM.
              This header is required if the API need to do Usher permission check
          schema:
            type: string
            example: 91D28437F82A4D028B45C98980B52951
      responses:
        '200':
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    title: SuccessfulExample
                    properties:
                      payload:
                        type: object
                        description: |-
                          The content data of the response.
                          This field is required if state is ‘S’
                        properties: {}
                      
                      state:
                        type: string
                        description: |-
                          The response status:
                          S: success
                          F: fail
                        example: S
                      
                  - $ref: '#/components/schemas/FailedExample'
              examples:
                SuccessfulExample:
                  summary: Successful Example
                  value:
                    payload: {}
                    
                    state: S
                    
                FailedExample:
                  summary: Failed Example
                  value:
                    errorForDisplay: This app does not exist.
                    errorCode: 400001
                    extraCode: 4001
                    extraMessage: IDM_session_expired
                    state: F
          description: Successful Response
          headers:
            Content-Type:
              description: Response Content Type
              schema: {type: string, default: application/json}
        '503':
          description: Service Unavailable
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required: [vinList]
              properties:
                vinList:
                  type: array
                  items:
                    type: object
                    required: [garageName, vin, garageAddress, garageStreet1, garageCity, garageState, garageZip, sellingDealerName, servicingDealerName,addressId,sellingDealerCode,servicingDealerCode]
                    properties:
                      garageName: {type: string, description: Name of the garage,
                        example: Garage GHI}
                      garageStreet2: {type: string, description: Street line 2 of
                          the garage address, example: Suite 400}
                      garageStreet1: {type: string, description: Street line 1 of
                          the garage address, example: 789 Oak Dr}
                      sellingDealerName: {type: string, description: Name of the selling
                          dealer, example: Dealer OPQ}
                      vin: {type: string, description: VIN number, example: 3C6JR6DT2CG123456}
                      servicingDealerName: {type: string, description: Name of the
                          servicing dealer, example: Dealer OPQ}
                      garageZip: {type: string, description: Zip code of the garage,
                        example: '11223'}
                      garageState: {type: string, description: State of the garage,
                        example: TX}
                      garageCity: {type: string, description: City of the garage,
                        example: Elsewhere}
                      garageAddress: {type: string, description: Address of the garage,
                        example: 789 Oak Dr}
                      addressId: {type: string, description: Address Number,
                        example: 87856}
                      sellingDealerCode: {type: string, description: Code of the selling dealer,
                        example: ABC123}
                      vehicleNickname: {type: string, description: Vehicle Nickname,
                        example: My Car}
                      servicingDealerCode: {type: string, description: Code of the servicing dealer,
                        example: ABC456}
                      bodyType: {type: string, description: body type,
                        example: abc}
                      bodyTypeId: {type: integer, description: body type id,
                        example: 12}
                        
        description: ''
        required: true       
components:
  schemas:
    FailedExample:
      type: object
      properties:
        errorForDisplay:
          type: string
          description: |-
            The error message.
            This field is required if state is ‘F’.
          example: Logged out for timeout.
        extraCode: {type: string, description: The error code return from
            3rd service provider, example: IDM_session_expired}
        extraMessage: {type: string, description: The error message return
            from 3rd service provider., example: IDM_session_expired}
        errorCode:
          oneOf:
           - $ref: '#/components/schemas/BusinessLogicErrors'
           - $ref: '#/components/schemas/CommonErrorCode'
          maxLength: 6
        state:
          type: string
          description: |-
            The response status:
            S: success
            F: failed
          example: F
    BusinessLogicErrors:
      type: integer
      description: |
          The error code. This field is required if the state is 'F'.
          - `910000`:`Could not execute request because system error occurred`
          - `910400`:`Could not execute request because system error occurred`
          - `910500`:`Could not execute request because system error occurred`
          - `910600`:`It is taking longer than normal to respond. Please try again later.`
          - `910001`:`Vin does not exist`
          - `910002`:`Not an isuzu vin`
          - `910003`:`Get auth code fail`
          - `910004`:`Verify auth code fail`
          - `910005`:`VIN has already been linked`
          - `910006`:`Gatex id is empty`
          - `910007`:`User does not exist`
          - `910008`:`Get sap code fail`
          - `910009`:`xxxx vin does not exist`
          - `910010`:`Get tac fail`
          - `910011`:`xxxx vin has not been linked`
          - `910012`:`Tac version is invalid`
          - `910013`: `Call Gatex register garage fail`
          - `910014`: `Call Gatex add vehicle fail`
          - `910015`: `Call gatex register gatex id fail`
          - `910016`: `Call gatex register company fail`
          - `910017`: `Company has been registered`
          - `910018`: `Do not have invitation permission`
          - `910019`: `Invitation code does not exist`
          - `910020`: `Invitation has been expired`
          - `910021`: `Cannot modify your own permissions`
          - `910022`: `Garage is not under the current administrator's management`
          - `910023`: `IDM logout fail`
          - `910024`: `The secondary user has been registered`
          - `910025`: `The secondary user is not under the current administrator's company`
      example: '910000'
    CommonErrorCode:
      type: integer
      description: |
          The common API error code.This field is required if state is 'F'.
          - `400001`:`This app does not exist.`
          - `400002`:`This application does not have the permission to access this API.`
          - `400005`:`This API does not support the method.`
          - `401001`:`Timestamp is empty.`
          - `401005`:`Timestamp is not valid.`
          - `400011`:`This API does not exist.`
          - `400012`:`Required parameter is missing.`
          - `400017`:`Authorization check failed.`
          - `400018`:`Authorization token is not set or expired.`
          - `401001`:`The knt-timestamp is empty.`
          - `401005`:`The knt-timestamp is not valid.`
          - `401004`:`The knt-app-key is empty.`
          - `600002`:`Access Token is invalid or expired!`
          - `600004`:`Access Token is empty!`
          - `700000`:`Server maintenance is in progress.`
      example: 600002
        
